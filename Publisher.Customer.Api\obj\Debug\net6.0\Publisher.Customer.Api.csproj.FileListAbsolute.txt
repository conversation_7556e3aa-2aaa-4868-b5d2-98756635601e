C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\appsettings.Development.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\appsettings.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Publisher.Customer.Api.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Publisher.Customer.Api.exe
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Publisher.Customer.Api.deps.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Publisher.Customer.Api.runtimeconfig.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Publisher.Customer.Api.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Publisher.Customer.Api.pdb
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Azure.Core.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Azure.Core.Amqp.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Azure.Messaging.ServiceBus.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\FluentValidation.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Azure.Amqp.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.InMemory.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\System.ClientModel.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\System.Collections.Immutable.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\bin\Debug\net6.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\scopedcss\bundle\Publisher.Customer.Api.styles.css
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets\msbuild.Publisher.Customer.Api.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets\msbuild.Publisher.Customer.Api.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets\msbuild.build.Publisher.Customer.Api.props
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets\msbuild.buildMultiTargeting.Publisher.Customer.Api.props
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets\msbuild.buildTransitive.Publisher.Customer.Api.props
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publishe.7D1DB316.Up2Date
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\refint\Publisher.Customer.Api.dll
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.pdb
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\Publisher.Customer.Api.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Publisher.Customer\Publisher.Customer.Api\obj\Debug\net6.0\ref\Publisher.Customer.Api.dll

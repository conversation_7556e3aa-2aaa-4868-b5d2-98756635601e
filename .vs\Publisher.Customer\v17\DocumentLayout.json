{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|c:\\users\\<USER>\\source\\repos\\publisher.customer\\publisher.customer.api\\features\\customers\\createcustomer\\createcustomerhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|solutionrelative:publisher.customer.api\\features\\customers\\createcustomer\\createcustomerhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|c:\\users\\<USER>\\source\\repos\\publisher.customer\\publisher.customer.api\\messages\\customermessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|solutionrelative:publisher.customer.api\\messages\\customermessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|c:\\users\\<USER>\\source\\repos\\publisher.customer\\publisher.customer.api\\infrastructure\\customerrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|solutionrelative:publisher.customer.api\\infrastructure\\customerrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|c:\\users\\<USER>\\source\\repos\\publisher.customer\\publisher.customer.api\\infrastructure\\customerdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E6DDF25-55F0-4CFC-A125-0F13D50C9198}|Publisher.Customer.Api\\Publisher.Customer.Api.csproj|solutionrelative:publisher.customer.api\\infrastructure\\customerdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "CustomerMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Messages\\CustomerMessage.cs", "RelativeDocumentMoniker": "Publisher.Customer.Api\\Messages\\CustomerMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Messages\\CustomerMessage.cs", "RelativeToolTip": "Publisher.Customer.Api\\Messages\\CustomerMessage.cs", "ViewState": "AgIAAA0AAAAAAAAAAAA8wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:54:51.669Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CustomerRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Infrastructure\\CustomerRepository.cs", "RelativeDocumentMoniker": "Publisher.Customer.Api\\Infrastructure\\CustomerRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Infrastructure\\CustomerRepository.cs", "RelativeToolTip": "Publisher.Customer.Api\\Infrastructure\\CustomerRepository.cs", "ViewState": "AgIAABEAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:54:42.066Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CustomerDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Infrastructure\\CustomerDbContext.cs", "RelativeDocumentMoniker": "Publisher.Customer.Api\\Infrastructure\\CustomerDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Infrastructure\\CustomerDbContext.cs", "RelativeToolTip": "Publisher.Customer.Api\\Infrastructure\\CustomerDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:54:41.082Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CreateCustomerHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Features\\Customers\\CreateCustomer\\CreateCustomerHandler.cs", "RelativeDocumentMoniker": "Publisher.Customer.Api\\Features\\Customers\\CreateCustomer\\CreateCustomerHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Publisher.Customer\\Publisher.Customer.Api\\Features\\Customers\\CreateCustomer\\CreateCustomerHandler.cs", "RelativeToolTip": "Publisher.Customer.Api\\Features\\Customers\\CreateCustomer\\CreateCustomerHandler.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAQwBMAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T01:54:35.698Z", "EditorCaption": ""}]}]}]}
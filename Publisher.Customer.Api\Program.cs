using System;
using System.Linq;
using System.Threading.Tasks;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Extensions;
using Publisher.Customer.Api.Features.Customers;
using Publisher.Customer.Api.Features.Customers.CreateCustomer;
using Publisher.Customer.Api.Features.Customers.DeleteCustomer;
using Publisher.Customer.Api.Features.Customers.GetCustomer;
using Publisher.Customer.Api.Features.Customers.GetCustomers;
using Publisher.Customer.Api.Features.Customers.UpdateCustomer;
using Publisher.Customer.Api.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add application services
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructure();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Log Service Bus configuration
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("Customer API started. Service Bus Topic: {TopicName}",
    app.Configuration["ServiceBus:CustomerTopicName"] ?? "customers");

// Customer API endpoints

// GET /api/customers
app.MapGet("/api/customers", async (HttpContext context) =>
{
    var handler = context.RequestServices.GetRequiredService<GetCustomersHandler>();
    var result = await handler.HandleAsync();

    if (result.IsSuccess)
    {
        await context.Response.WriteAsJsonAsync(result.Value);
    }
    else
    {
        context.Response.StatusCode = 400;
        await context.Response.WriteAsJsonAsync(new { error = result.Error });
    }
});

// GET /api/customers/{id}
app.MapGet("/api/customers/{id:int}", async (HttpContext context) =>
{
    var handler = context.RequestServices.GetRequiredService<GetCustomerHandler>();
    var id = int.Parse(context.Request.RouteValues["id"]!.ToString()!);
    var result = await handler.HandleAsync(id);

    if (result.IsSuccess)
    {
        await context.Response.WriteAsJsonAsync(result.Value);
    }
    else
    {
        context.Response.StatusCode = 404;
        await context.Response.WriteAsJsonAsync(new { error = result.Error });
    }
});

// POST /api/customers
app.MapPost("/api/customers", async (HttpContext context) =>
{
    var handler = context.RequestServices.GetRequiredService<CreateCustomerHandler>();
    var validator = context.RequestServices.GetRequiredService<IValidator<CreateCustomerRequest>>();

    var request = await context.Request.ReadFromJsonAsync<CreateCustomerRequest>();
    if (request == null)
    {
        context.Response.StatusCode = 400;
        await context.Response.WriteAsJsonAsync(new { error = "Invalid request body" });
        return;
    }

    var validationResult = await validator.ValidateAsync(request);
    if (!validationResult.IsValid)
    {
        context.Response.StatusCode = 400;
        await context.Response.WriteAsJsonAsync(new {
            error = "Validation failed",
            details = validationResult.Errors.Select(e => new { field = e.PropertyName, message = e.ErrorMessage })
        });
        return;
    }

    var result = await handler.HandleAsync(request);

    if (result.IsSuccess)
    {
        context.Response.StatusCode = 201;
        context.Response.Headers.Location = $"/api/customers/{result.Value.Customer.Id}";
        await context.Response.WriteAsJsonAsync(result.Value);
    }
    else
    {
        context.Response.StatusCode = 400;
        await context.Response.WriteAsJsonAsync(new { error = result.Error });
    }
});

// PUT /api/customers/{id}
app.MapPut("/api/customers/{id:int}", async (HttpContext context) =>
{
    var handler = context.RequestServices.GetRequiredService<UpdateCustomerHandler>();
    var validator = context.RequestServices.GetRequiredService<IValidator<UpdateCustomerRequest>>();

    var id = int.Parse(context.Request.RouteValues["id"]!.ToString()!);
    var request = await context.Request.ReadFromJsonAsync<UpdateCustomerRequest>();
    if (request == null)
    {
        context.Response.StatusCode = 400;
        await context.Response.WriteAsJsonAsync(new { error = "Invalid request body" });
        return;
    }

    var validationResult = await validator.ValidateAsync(request);
    if (!validationResult.IsValid)
    {
        context.Response.StatusCode = 400;
        await context.Response.WriteAsJsonAsync(new {
            error = "Validation failed",
            details = validationResult.Errors.Select(e => new { field = e.PropertyName, message = e.ErrorMessage })
        });
        return;
    }

    var result = await handler.HandleAsync(id, request);

    if (result.IsSuccess)
    {
        await context.Response.WriteAsJsonAsync(result.Value);
    }
    else
    {
        context.Response.StatusCode = 404;
        await context.Response.WriteAsJsonAsync(new { error = result.Error });
    }
});

// DELETE /api/customers/{id}
app.MapDelete("/api/customers/{id:int}", async (HttpContext context) =>
{
    var handler = context.RequestServices.GetRequiredService<DeleteCustomerHandler>();
    var id = int.Parse(context.Request.RouteValues["id"]!.ToString()!);
    var result = await handler.HandleAsync(id);

    if (result.IsSuccess)
    {
        context.Response.StatusCode = 204;
    }
    else
    {
        context.Response.StatusCode = 404;
        await context.Response.WriteAsJsonAsync(new { error = result.Error });
    }
});

app.Run();